# 版本更新时授权码重复使用问题修复

## 问题描述

在单点登录(SSO)回调页面中，当系统检测到版本更新时，会出现授权码重复使用的问题：

1. 应用启动时，`main.ts` 中的 `cacheManager.init()` 异步检测到版本更新
2. 更新版本信息并设置100ms延迟刷新
3. 在延迟刷新执行前，应用继续初始化，用户可以跳转到 `SsoCallback` 页面
4. `SsoCallback` 页面检测版本时，由于版本信息已更新，认为不需要刷新
5. 开始处理授权码，调用API换取token
6. 在token换取过程中，延迟刷新执行，页面重新加载
7. 页面重新加载后，授权码仍在URL中，但服务端已处理过，导致重复使用错误

## 解决方案

### 核心思路：在应用启动前完成版本检测

问题的根本原因是版本检测是异步的，应用在版本检测完成前就开始初始化。解决方案是让应用等待版本检测完成后再初始化。

### 修改的逻辑

#### main.ts
- 将 `cacheManager.init()` 移到 `setupAll()` 函数内部
- 使用 `await` 等待版本检测完成后再继续应用初始化
- 如果需要刷新，会在应用启动前刷新，避免用户进入应用后再刷新

#### CacheManager.init()
- 移除延迟刷新逻辑，改为立即刷新
- 简化代码，移除不必要的状态管理

#### SsoCallback.vue
- 移除重复的版本检测逻辑
- 简化代码，直接处理授权码

## 修改的文件

1. **src/main.ts**
   - 将 `cacheManager.init()` 移到应用初始化函数内部
   - 使用 `await` 等待版本检测完成

2. **src/utils/cacheManager.ts**
   - 移除延迟刷新逻辑，改为立即刷新
   - 简化代码，移除不必要的状态管理

3. **src/views/Login/SsoCallback.vue**
   - 移除重复的版本检测逻辑
   - 简化代码，直接处理授权码

## 工作流程

### 修复前的问题流程
1. `main.ts` 异步执行版本检测
2. 应用立即开始初始化，不等待版本检测完成
3. 用户可能在版本检测完成前跳转到 SsoCallback
4. 导致授权码处理与页面刷新冲突

### 修复后的正确流程
1. 应用启动时，先等待版本检测完成
2. 如果需要刷新，在应用初始化前就刷新
3. 如果不需要刷新，继续正常的应用初始化
4. 用户访问 SsoCallback 时，版本检测已完成，直接处理授权码

## 核心改进

1. **同步化版本检测**：应用等待版本检测完成后再初始化
2. **简化逻辑**：移除复杂的延迟刷新状态管理
3. **提前刷新**：在用户进入应用前完成必要的刷新

## 相关代码示例

### main.ts 修改前
```typescript
// 异步执行，不等待完成
cacheManager.init().catch((error) => {
  console.error('缓存管理器初始化失败:', error)
})

const setupAll = async () => {
  // 立即开始应用初始化
  const app = createApp(App)
  // ...
}
```

### main.ts 修改后
```typescript
const setupAll = async () => {
  // 先等待版本检测完成
  try {
    await cacheManager.init()
  } catch (error) {
    console.error('缓存管理器初始化失败:', error)
  }

  // 版本检测完成后再初始化应用
  const app = createApp(App)
  // ...
}
```

这个修复从根本上解决了版本更新与授权码处理的时序冲突问题，确保了系统的稳定性。
