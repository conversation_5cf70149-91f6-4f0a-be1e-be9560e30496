/*
 * @Author: AI Assistant
 * @Date: 2025-07-03
 * @Description: 缓存管理工具，用于处理版本更新时的缓存清理和打包信息输出
 */

import { useCache } from '@/hooks/web/useCache'

// 声明全局变量类型
declare const __APP_INFO__: {
  buildTime: string
  GIT_COMMIT_DATE: string
  GIT_HASH: string
  GIT_LAST_COMMIT_MESSAGE: string
  GIT_TAG: string
}

const { wsCache } = useCache()

export class CacheManager {
  private static instance: CacheManager
  private readonly VERSION_KEY = 'app_version'
  private readonly DEPLOYMENT_TIME_KEY = 'deployment_time'

  static getInstance(): CacheManager {
    if (!CacheManager.instance) {
      CacheManager.instance = new CacheManager()
    }
    return CacheManager.instance
  }

  /**
   * 检查是否需要刷新页面
   * @returns boolean 是否需要刷新页面
   */
  checkNeedRefresh(): boolean {
    try {
      const currentVersion = this.getCurrentVersion()
      const storedVersion = this.getStoredVersion()
      const currentDeploymentTime = this.getCurrentDeploymentTime()
      const storedDeploymentTime = this.getStoredDeploymentTime()

      // 如果缓存中没有版本信息，可能是第一次访问或缓存已被清理，不执行刷新
      if (!storedVersion || !storedDeploymentTime) {
        console.log('缓存中没有版本信息，可能是第一次访问或缓存已被清理')
        return false
      }

      // 如果版本号或部署时间不匹配，需要刷新页面
      const needRefresh =
        storedVersion !== currentVersion || storedDeploymentTime !== currentDeploymentTime

      if (needRefresh) {
        console.log('检测到版本不匹配:', {
          currentVersion,
          storedVersion,
          currentDeploymentTime,
          storedDeploymentTime
        })
      }

      return needRefresh
    } catch (error) {
      console.error('检查缓存版本失败:', error)
      return false // 出错时不执行刷新，避免无限刷新
    }
  }

  /**
   * 使用wsCache更新版本信息
   */
  updateVersionInfo(): void {
    try {
      const currentVersion = this.getCurrentVersion()
      const currentDeploymentTime = this.getCurrentDeploymentTime()

      // 使用wsCache更新版本信息
      wsCache.set(this.VERSION_KEY, currentVersion)
      wsCache.set(this.DEPLOYMENT_TIME_KEY, currentDeploymentTime)

      console.log('版本信息已更新:', { currentVersion, currentDeploymentTime })
    } catch (error) {
      console.error('更新版本信息失败:', error)
    }
  }

  /**
   * 获取当前应用版本
   */
  private getCurrentVersion(): string {
    try {
      return __APP_INFO__.buildTime || new Date().toISOString()
    } catch {
      return new Date().toISOString()
    }
  }

  /**
   * 获取存储的版本
   */
  private getStoredVersion(): string | null {
    return wsCache.get(this.VERSION_KEY) || localStorage.getItem(this.VERSION_KEY)
  }

  /**
   * 获取当前部署时间
   */
  private getCurrentDeploymentTime(): string {
    try {
      return __APP_INFO__.GIT_COMMIT_DATE || new Date().toISOString()
    } catch {
      return new Date().toISOString()
    }
  }

  /**
   * 获取存储的部署时间
   */
  private getStoredDeploymentTime(): string | null {
    return wsCache.get(this.DEPLOYMENT_TIME_KEY) || localStorage.getItem(this.DEPLOYMENT_TIME_KEY)
  }

  /**
   * 输出打包信息到控制台
   */
  printBuildInfo(): void {
    try {
      const buildInfo = this.getBuildInfo()

      // Git标签的样式
      const tagStyles = [
        'color: white',
        'background: linear-gradient(45deg, #ff6b6b, #4ecdc4)',
        'font-size: 14px',
        'font-weight: bold',
        'border: 2px solid #fff',
        'border-radius: 8px',
        'text-shadow: 1px 1px 2px rgba(0,0,0,0.5)',
        'padding: 4px 8px',
        'margin: 2px'
      ].join(';')

      // 构建时间的样式
      const buildTimeStyles = [
        'color: white',
        'background: linear-gradient(45deg, #667eea, #764ba2)',
        'font-size: 14px',
        'font-weight: bold',
        'border: 2px solid #fff',
        'border-radius: 8px',
        'text-shadow: 1px 1px 2px rgba(0,0,0,0.5)',
        'padding: 4px 8px',
        'margin: 2px'
      ].join(';')

      console.group('📦 应用构建信息')
      console.log(`%c🕒 构建时间: ${buildInfo.buildTime}`, buildTimeStyles)
      console.log(`%c🏷️  Git标签: ${buildInfo.gitTag}`, tagStyles)
      console.log('📝 Git哈希:', buildInfo.gitHash)
      console.log('💬 最后提交:', buildInfo.lastCommitMessage)
      console.log('📅 提交时间:', buildInfo.commitDate)
      console.log('🌍 当前环境:', this.getCurrentEnvironment())
      console.groupEnd()
    } catch (error) {
      console.warn('无法获取构建信息:', error)
    }
  }

  /**
   * 获取构建信息
   */
  private getBuildInfo() {
    try {
      return {
        buildTime: __APP_INFO__.buildTime || '未知',
        gitTag: __APP_INFO__.GIT_TAG || '未知',
        gitHash: __APP_INFO__.GIT_HASH?.substring(0, 8) || '未知',
        lastCommitMessage: __APP_INFO__.GIT_LAST_COMMIT_MESSAGE || '未知',
        commitDate: __APP_INFO__.GIT_COMMIT_DATE || '未知'
      }
    } catch {
      return {
        buildTime: '未知',
        gitTag: '未知',
        gitHash: '未知',
        lastCommitMessage: '未知',
        commitDate: '未知'
      }
    }
  }

  /**
   * 获取当前环境
   */
  private getCurrentEnvironment(): string {
    try {
      if (import.meta.env.DEV) {
        return `开发环境 (${import.meta.env.MODE})`
      }
      return `生产环境 (${import.meta.env.MODE})`
    } catch {
      return '未知环境'
    }
  }

  /**
   * 从服务端检查版本信息
   */
  private async checkServerVersion(): Promise<boolean> {
    try {
      // 添加时间戳防止缓存
      const timestamp = Date.now()
      const response = await fetch(`/version.json?t=${timestamp}`, {
        method: 'GET',
        cache: 'no-cache',
        headers: {
          'Cache-Control': 'no-cache',
          Pragma: 'no-cache'
        }
      })

      if (response.ok) {
        const serverVersion = await response.json()
        const currentVersion = this.getCurrentVersion()

        console.log('服务端版本检测:', {
          server: serverVersion.buildTime,
          current: currentVersion
        })

        return serverVersion.buildTime !== currentVersion
      }
    } catch (error) {
      console.warn('无法从服务端检查版本:', error)
    }
    return false
  }

  /**
   * 初始化缓存管理
   */
  async init(): Promise<void> {
    try {
      // 输出构建信息
      this.printBuildInfo()

      const storedVersion = this.getStoredVersion()
      const storedDeploymentTime = this.getStoredDeploymentTime()

      // 如果缓存中没有版本信息，说明是第一次访问或缓存已被清理
      if (!storedVersion || !storedDeploymentTime) {
        console.log('首次访问或缓存已被清理，保存当前版本信息')
        this.updateVersionInfo()
        return
      }

      // 首先检查本地版本
      let needRefresh = this.checkNeedRefresh()

      // 如果本地检测没有发现变化，尝试从服务端检查
      if (!needRefresh) {
        console.log('本地版本检测无变化，检查服务端版本...')
        needRefresh = await this.checkServerVersion()
      }

      if (needRefresh) {
        console.log('检测到版本不匹配，更新版本信息并刷新页面')
        this.updateVersionInfo()

        // 立即刷新页面
        console.log('执行页面刷新')
        window.location.reload()
      } else {
        console.log('版本检测通过，无需刷新')
      }
    } catch (error) {
      console.error('初始化缓存管理失败:', error)
    }
  }
}

// 导出单例实例
export const cacheManager = CacheManager.getInstance()
