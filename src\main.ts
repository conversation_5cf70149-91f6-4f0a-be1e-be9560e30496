/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 08:56:30
 * @LastEditors: HoJack
 * @LastEditTime: 2024-01-03 17:23:17
 * @Description:
 */
// 引入windi css
import '@/plugins/windi.css'

// 导入全局的svg图标
import '@/plugins/svgIcon'

// 初始化多语言
import { setupI18n } from '@/plugins/vueI18n'

// 引入状态管理
import { setupStore } from '@/store'

// 全局组件
import { setupGlobCom } from '@/components'

// 引入 form-create
import { setupFormCreate } from '@/plugins/formCreate'

// 引入element-plus
import { setupElementPlus } from '@/plugins/elementPlus'
import 'element-plus/dist/index.css' //引入element-plus样式

// 引入全局样式
import '@/styles/index.less'

// 引入vform表单设计器
import { setupVForm } from '@/plugins/vform'
import '../lib/vform/designer.style.css'

// 引入动画
import '@/plugins/animate.css'

// 引入微前端
import { setupMicroApps } from '@/plugins/microApp'

// 路由
import { setupRouter } from './router'

// 权限
import { setupPermission } from './directives'

import { createApp } from 'vue'

import App from './App.vue'

// 导入缓存管理器
import { cacheManager } from '@/utils/cacheManager'

import './permission'

// 创建实例
const setupAll = async () => {
  // 在应用初始化前先进行版本检测
  try {
    await cacheManager.init()
  } catch (error) {
    console.error('缓存管理器初始化失败:', error)
  }
  const app = createApp(App)

  await setupI18n(app)

  // 设置微前端 - 在 Vue 应用创建后初始化
  setupMicroApps()

  setupStore(app)

  setupGlobCom(app)

  setupElementPlus(app)

  setupFormCreate(app)

  setupRouter(app)

  setupPermission(app)

  setupVForm(app)

  app.mount('#app')
}

setupAll()
